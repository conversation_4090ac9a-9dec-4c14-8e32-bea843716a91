'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useRouter, useSearchParams } from '@/i18n/routing';
import { AlertCircle, RefreshCw } from 'lucide-react';
import { useTranslations } from 'next-intl';
import React from 'react';

const VerificationErrorPage = () => {
  const t = useTranslations('pages.VerificationErrorPage');
  const router = useRouter();
  const searchParams = useSearchParams();
  const error = searchParams.get('error') || 'Unknown error occurred';

  return (
    <div className="flex h-full grow flex-col items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardContent className="flex flex-col items-center space-y-6 p-8 text-center">
          <div className="flex h-20 w-20 items-center justify-center rounded-full bg-red-100">
            <AlertCircle className="h-10 w-10 text-red-600" />
          </div>
          
          <div className="space-y-2">
            <h1 className="text-2xl font-bold text-red-800">
              {t('title', { default: 'Verification Failed' })}
            </h1>
            <p className="text-gray-600">
              {t('subtitle', { 
                default: 'We encountered an issue while verifying your email address.' 
              })}
            </p>
            <div className="mt-4 rounded-lg bg-red-50 p-3">
              <p className="text-sm text-red-700">
                <strong>Error:</strong> {error}
              </p>
            </div>
          </div>

          <div className="w-full space-y-3">
            <Button
              onClick={() => router.push('/auth/confirmation')}
              className="w-full bg-primary hover:bg-primary/90"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              {t('retryButton', { default: 'Try Again' })}
            </Button>
            
            <Button
              variant="outline"
              onClick={() => router.push('/auth/sign-up')}
              className="w-full"
            >
              {t('signUpButton', { default: 'Back to Sign Up' })}
            </Button>
            
            <Button
              variant="ghost"
              onClick={() => router.push('/support')}
              className="w-full text-sm"
            >
              {t('supportButton', { default: 'Contact Support' })}
            </Button>
          </div>

          <div className="text-xs text-gray-500">
            {t('helpText', { 
              default: 'If you continue to experience issues, please contact our support team for assistance.' 
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default VerificationErrorPage;
